<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/8/4
 * Time: 12:26
 */

namespace App\Utils;

use Hyperf\Codec\Json;
use Hyperf\Context\ApplicationContext;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;

/**
 * Class Log
 * @method static void info($message, array $context = [])
 * @method static void debug($message, array $context = [])
 * @method static void warning($message, array $context = [])
 * @method static void error($message, array $context = [])
 * @method static void emergency($message, array $context = [])
 * @method static void alert($message, array $context = [])
 * @method static void critical($message, array $context = [])
 * @method static void notice($message, array $context = [])
 * @package App\Utils
 */
class Log
{
    public static function __callStatic(string $name, array $arguments)
    {
        return self::get()->{$name}(...$arguments);
    }
    public static function get(string $name = 'app', string $group = 'default'): LoggerInterface
    {
        $logger = ApplicationContext::getContainer()->get(LoggerFactory::class);
        if (\Hyperf\Config\config('app.forceStdoutLog') == 'true') { // pdd 需要强制输出到stdout
            return $logger->get($name,'stdout');
        }
        return $logger->get($name, $group);
    }

    public static function getRequest(string $name = 'request'): LoggerInterface
    {
        return self::get($name,'request');
    }

    /**
     * 记录异常错误日志
     * @param string $msg
     * @param \Throwable $e
     * @param array $data
     * @return void
     */
    public static function errorException(string $msg, \Throwable $e, array $data = []): void
    {
        $msg = $msg . ':' . $e->getMessage();
        $trace = $e->getTrace();
        if (!Environment::isProd()) {
            $trace = "\n" . join("\n", array_map(function ($item) {
                    $class = $item['class'] ?? '[class]';
                    $args = Json::encode($item['args'] ?? []);
                    $args = stripslashes($args);
                    $file = $item['file'] ?? '[file]';
                    $line = $item['line'] ?? '[line]';
                    $type = $item['type'] ?? '[type]';
                    $function = $item['function'] ?? '[function]';
                    return "{$file}:{$line};{$class}{$type}{$function}();args:$args";
                }, $e->getTrace()));
        }
        $context = [
            'data' => $data,
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $trace,
        ];
        self::error($msg, $context);
    }
}