## 代码生成器

MineAdmin 3.0 版本代码生成器插件。

## 下载插件

- 后台应用市场下载插件
- 命令安装，在后端根目录下执行命令：

```bash
php bin/hyperf.php mine-extension:download mine-admin/code-generator
```
## 安装插件

- 后台应用商店安装插件
- 命令安装，在后端根目录下执行命令：

```bash
php bin/hyperf.php mine-extension:install mine-admin/code-generator --yes
```


## 注意事项
如果报错，可能是前端缺少依赖，请在前端根目录下执行以下命令：
```bash
pnpm i ace-builds vue3-ace-editor -D
```

## 使用介绍

### 配置信息
| 配置项     | 配置项说明                                                                                                                     |
|:--------|---------------------------------------------------------------------------------------------------------------------------|
| 表名称     | 要配置的数据表，自动读取无需且不能修改                                                                                                       |
| 菜单路由    | 展示在浏览器地址栏里面的URL，非必填，不填写则默认使用大驼峰格式的数据表名                                                                                    |
| api路由别名 | 后端接口地址URL，非必填，不填写则默认使用数据表名称                                                                                               |
| 后端子目录   | 非必填，在某个包（模块）功能比较多时，可以将功能分割成不同的子模块，使用子目录实现，填写后，将跟在“包名”后面，此输入框处无需填写“包名（模块名）”                                                |
| 前端子目录   | 非必填，在某个包（模块）功能比较多时，可以将功能分割成不同的子模块，使用子目录实现，填写后，仅针对views路径，将跟在“包名/views/”后面，此输入框无需填写“包名（模块名）”                               |
| 备注信息    | 备注信息                                                                                                                      |
| 所属菜单    | 选择所属菜单                                                                                                                    |
| 菜单名称    | 菜单名称                                                                                                                      |
| 菜单国际化   | 菜单国际化配置，如：baseMenu.permission.index，填写的国际化配置未在配置文件中提前录入时，会展示为配置的国际化字符串如：baseMenu.permission.index，对系统无影响，但会报警告            |
| 包名      | 用于区分一个大的功能模块，比如商城模块、OA模块                                                                                                  |
| api路由方式 | 可以选择控制器使用swagger还是hyperf的路由注解，生成代码时，两种都会生成，只不过另外一种会被注释，用于保留后面的后悔余地。例如在修改控制器代码后，想要更换路由方式，则无需重新生成代码造成代码丢失，通过关闭、打开注释即可更换路由方式 |
| 字段配置    | 配置每个字段在新增表单、修改表单、搜索框等的配置，建议配置国际化                                                                                          |
