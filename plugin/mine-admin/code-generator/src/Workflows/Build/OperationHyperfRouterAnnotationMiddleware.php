<?php

namespace Plugin\MineAdmin\CodeGenerator\Workflows\Build;

use Plugin\MineAdmin\CodeGenerator\Model\CodeGenerator;

final class OperationHyperfRouterAnnotationMiddleware extends AbstractBuilder
{
    protected string $viewViewTemplate = 'code-generator.operationhyperfrouterannotationmiddleware';

    protected function formatRelativePath(CodeGenerator $codeGenerator): string
    {
        return 'app/Http/Common/Middleware';
    }

    protected function formatFilename(CodeGenerator $codeGenerator): string
    {
        return 'OperationHyperfRouterAnnotationMiddleware.php';
    }
}